import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";

interface Photo {
  id: string;
  url: string;
  caption: string;
  user_id: string;
  profiles: {
    display_name: string;
    username: string;
  };
}

export const PhotoSwiper = () => {
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [rating, setRating] = useState<number | null>(null);
  const { toast } = useToast();
  const { user } = useAuth();

  useEffect(() => {
    fetchPhotos();
  }, [user]);

  const fetchPhotos = async () => {
    if (!user) return;

    try {
      // Get photos that the user hasn't rated yet
      const { data: ratedPhotoIds } = await supabase
        .from("ratings")
        .select("photo_id")
        .eq("user_id", user.id);

      const ratedIds = ratedPhotoIds?.map((r) => r.photo_id) || [];

      let query = supabase
        .from("photos")
        .select(`
          id,
          url,
          caption,
          user_id,
          profiles!inner (
            display_name,
            username
          )
        `)
        .neq("user_id", user.id); // Don't show user's own photos

      if (ratedIds.length > 0) {
        query = query.not("id", "in", `(${ratedIds.join(",")})`);
      }

      const { data, error } = await query.limit(10);

      if (error) throw error;
      setPhotos(data as any || []);
    } catch (error: any) {
      toast({
        title: "Error",
        description: "Failed to load photos",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const submitRating = async (ratingValue: number) => {
    if (!user || !photos[currentIndex]) return;

    try {
      const { error } = await supabase.from("ratings").insert({
        user_id: user.id,
        photo_id: photos[currentIndex].id,
        rating: ratingValue,
      });

      if (error) throw error;

      setRating(ratingValue);
      
      // Move to next photo after a short delay
      setTimeout(() => {
        setCurrentIndex((prev) => prev + 1);
        setRating(null);
      }, 1500);

      toast({
        title: "Rating submitted!",
        description: `You rated this photo ${ratingValue}/10`,
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: "Failed to submit rating",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (photos.length === 0 || currentIndex >= photos.length) {
    return (
      <div className="text-center p-8">
        <h2 className="text-2xl font-bold mb-4">No more photos to rate!</h2>
        <p className="text-muted-foreground mb-4">
          Check back later for new photos to rate.
        </p>
        <Button onClick={fetchPhotos}>Refresh</Button>
      </div>
    );
  }

  const currentPhoto = photos[currentIndex];

  return (
    <div className="max-w-md mx-auto space-y-6">
      <Card className="overflow-hidden">
        <CardContent className="p-0">
          <div className="aspect-square relative">
            <img
              src={currentPhoto.url}
              alt={currentPhoto.caption || "Photo"}
              className="w-full h-full object-cover"
            />
          </div>
          <div className="p-4">
            <h3 className="font-semibold text-lg">
              {currentPhoto.profiles?.display_name || currentPhoto.profiles?.username}
            </h3>
            {currentPhoto.caption && (
              <p className="text-muted-foreground mt-1">{currentPhoto.caption}</p>
            )}
          </div>
        </CardContent>
      </Card>

      <div className="space-y-4">
        <h3 className="text-center text-lg font-semibold">Rate this photo (1-10)</h3>
        <div className="grid grid-cols-5 gap-2">
          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((value) => (
            <Button
              key={value}
              variant={rating === value ? "default" : "outline"}
              onClick={() => submitRating(value)}
              disabled={rating !== null}
              className="aspect-square"
            >
              {value}
            </Button>
          ))}
        </div>
      </div>

      <div className="text-center text-sm text-muted-foreground">
        Photo {currentIndex + 1} of {photos.length}
      </div>
    </div>
  );
};