import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";
import { motion, AnimatePresence } from "framer-motion";
import { Star, Heart, Zap } from "lucide-react";

interface Photo {
  id: string;
  url: string;
  caption: string;
  user_id: string;
  total_ratings: number;
  average_rating: number;
  profiles: {
    display_name: string;
    username: string;
  };
}

export const PhotoSwiper = () => {
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [rating, setRating] = useState<number | null>(null);
  const [pointsEarned, setPointsEarned] = useState<number | null>(null);
  const [showFeedback, setShowFeedback] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();

  useEffect(() => {
    fetchPhotos();
  }, [user]);

  const fetchPhotos = async () => {
    if (!user) return;

    try {
      // Get photos that the user hasn't rated yet
      const { data: ratedPhotoIds } = await supabase
        .from("ratings")
        .select("photo_id")
        .eq("user_id", user.id);

      const ratedIds = ratedPhotoIds?.map((r) => r.photo_id) || [];

      // First get photos, then get profile data separately
      let query = supabase
        .from("photos")
        .select(`
          id,
          url,
          caption,
          user_id,
          total_ratings,
          average_rating
        `)
        .neq("user_id", user.id); // Don't show user's own photos

      if (ratedIds.length > 0) {
        query = query.not("id", "in", `(${ratedIds.join(",")})`);
      }

      const { data: photosData, error } = await query.limit(10);

      if (error) throw error;

      if (photosData && photosData.length > 0) {
        // Get profile data for all photo owners
        const userIds = photosData.map(photo => photo.user_id);
        const { data: profilesData } = await supabase
          .from("profiles")
          .select("user_id, display_name, username")
          .in("user_id", userIds);

        // Combine photos with profile data
        const photosWithProfiles = photosData.map(photo => ({
          ...photo,
          profiles: profilesData?.find(profile => profile.user_id === photo.user_id) || {
            display_name: "Unknown User",
            username: "unknown"
          }
        }));

        setPhotos(photosWithProfiles);
      } else {
        setPhotos([]);
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: "Failed to load photos",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const calculatePoints = (userRating: number, averageRating: number, totalRatings: number): number => {
    if (totalRatings === 0) return 5; // Base points for first rating

    const difference = Math.abs(userRating - averageRating);
    const maxPoints = 20;
    const minPoints = 1;

    // More points for being closer to the average
    const accuracy = Math.max(0, 1 - (difference / 9)); // 9 is max possible difference
    const points = Math.round(minPoints + (maxPoints - minPoints) * accuracy);

    return points;
  };

  const submitRating = async (ratingValue: number) => {
    if (!user || !photos[currentIndex] || isSubmitting) return;

    setIsSubmitting(true);
    setRating(ratingValue);

    try {
      const currentPhoto = photos[currentIndex];
      const points = calculatePoints(ratingValue, currentPhoto.average_rating, currentPhoto.total_ratings);

      // Insert rating with calculated points
      const { error: ratingError } = await supabase.from("ratings").insert({
        user_id: user.id,
        photo_id: currentPhoto.id,
        rating: ratingValue,
        points_earned: points,
      });

      if (ratingError) throw ratingError;

      // Update user's total points
      const { data: currentProfile, error: fetchError } = await supabase
        .from('profiles')
        .select('points')
        .eq('user_id', user.id)
        .single();

      if (!fetchError && currentProfile) {
        const { error: profileError } = await supabase
          .from('profiles')
          .update({ points: currentProfile.points + points })
          .eq('user_id', user.id);

        if (profileError) console.warn("Failed to update user points:", profileError);
      }

      // Update photo's average rating
      const newTotalRatings = currentPhoto.total_ratings + 1;
      const newAverageRating = ((currentPhoto.average_rating * currentPhoto.total_ratings) + ratingValue) / newTotalRatings;

      const { error: photoUpdateError } = await supabase
        .from("photos")
        .update({
          total_ratings: newTotalRatings,
          average_rating: newAverageRating,
        })
        .eq("id", currentPhoto.id);

      if (photoUpdateError) console.warn("Failed to update photo stats:", photoUpdateError);

      setPointsEarned(points);
      setShowFeedback(true);

      // Move to next photo after showing feedback
      setTimeout(() => {
        setCurrentIndex((prev) => prev + 1);
        setRating(null);
        setPointsEarned(null);
        setShowFeedback(false);
        setIsSubmitting(false);
      }, 2500);

      toast({
        title: "Rating submitted!",
        description: `You earned ${points} points! (${ratingValue}/10)`,
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: "Failed to submit rating",
        variant: "destructive",
      });
      setRating(null);
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (photos.length === 0 || currentIndex >= photos.length) {
    return (
      <div className="text-center p-8">
        <h2 className="text-2xl font-bold mb-4">No more photos to rate!</h2>
        <p className="text-muted-foreground mb-4">
          Check back later for new photos to rate.
        </p>
        <Button onClick={fetchPhotos}>Refresh</Button>
      </div>
    );
  }

  const currentPhoto = photos[currentIndex];

  return (
    <div className="max-w-md mx-auto space-y-6 relative">
      <AnimatePresence mode="wait">
        <motion.div
          key={currentIndex}
          initial={{ opacity: 0, x: 300 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -300 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="overflow-hidden relative">
            <CardContent className="p-0">
              <div className="aspect-square relative">
                <img
                  src={currentPhoto.url}
                  alt={currentPhoto.caption || "Photo"}
                  className="w-full h-full object-cover"
                />

                {/* Rating overlay when submitted */}
                <AnimatePresence>
                  {showFeedback && rating && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.5 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.5 }}
                      className="absolute inset-0 bg-black/50 flex items-center justify-center"
                    >
                      <motion.div
                        initial={{ y: 20 }}
                        animate={{ y: 0 }}
                        className="text-center text-white"
                      >
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 0.5 }}
                          className="text-4xl mb-2"
                        >
                          {pointsEarned && pointsEarned >= 15 ? "🎯" : pointsEarned && pointsEarned >= 10 ? "⭐" : "👍"}
                        </motion.div>
                        <div className="text-xl font-bold">+{pointsEarned} points!</div>
                        <div className="text-sm">Rating: {rating}/10</div>
                      </motion.div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              <div className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold text-lg">
                      {currentPhoto.profiles?.display_name || currentPhoto.profiles?.username}
                    </h3>
                    {currentPhoto.caption && (
                      <p className="text-muted-foreground mt-1">{currentPhoto.caption}</p>
                    )}
                  </div>
                  <div className="text-right text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3" />
                      {currentPhoto.total_ratings > 0 ? currentPhoto.average_rating.toFixed(1) : "New"}
                    </div>
                    <div>{currentPhoto.total_ratings} ratings</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </AnimatePresence>

      <div className="space-y-4">
        <h3 className="text-center text-lg font-semibold">Rate this photo (1-10)</h3>
        <div className="grid grid-cols-5 gap-2">
          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((value) => (
            <motion.div
              key={value}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                variant={rating === value ? "default" : "outline"}
                onClick={() => submitRating(value)}
                disabled={isSubmitting}
                className="aspect-square w-full"
              >
                {value}
              </Button>
            </motion.div>
          ))}
        </div>
      </div>

      <div className="text-center text-sm text-muted-foreground">
        Photo {currentIndex + 1} of {photos.length}
      </div>
    </div>
  );
};