import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Trophy, Medal, Award, Star, Zap, Target } from "lucide-react";
import { motion } from "framer-motion";
import { useAuth } from "@/hooks/useAuth";

interface LeaderboardEntry {
  id: string;
  username: string;
  display_name: string;
  points: number;
  level: number;
  user_id: string;
}

interface UserBadge {
  id: string;
  name: string;
  description: string;
  icon: string;
  earned_at: string;
}

export const Leaderboard = () => {
  const [leaders, setLeaders] = useState<LeaderboardEntry[]>([]);
  const [userBadges, setUserBadges] = useState<UserBadge[]>([]);
  const [userStats, setUserStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  useEffect(() => {
    fetchLeaderboard();
    if (user) {
      fetchUserBadges();
      fetchUserStats();
    }
  }, [user]);

  const fetchLeaderboard = async () => {
    try {
      const { data, error } = await supabase
        .from("profiles")
        .select("id, username, display_name, points, level, user_id")
        .order("points", { ascending: false })
        .limit(10);

      if (error) throw error;
      setLeaders(data || []);
    } catch (error) {
      console.error("Error fetching leaderboard:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchUserBadges = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from("user_badges")
        .select(`
          id,
          earned_at,
          badges (
            name,
            description,
            icon
          )
        `)
        .eq("user_id", user.id);

      if (error) throw error;

      const badges = data?.map(item => ({
        id: item.id,
        name: item.badges.name,
        description: item.badges.description,
        icon: item.badges.icon,
        earned_at: item.earned_at,
      })) || [];

      setUserBadges(badges);
    } catch (error) {
      console.error("Error fetching user badges:", error);
    }
  };

  const fetchUserStats = async () => {
    if (!user) return;

    try {
      // Get user's current profile
      const { data: profile } = await supabase
        .from("profiles")
        .select("points, level")
        .eq("user_id", user.id)
        .single();

      // Get user's rating count
      const { count: ratingsCount } = await supabase
        .from("ratings")
        .select("*", { count: "exact", head: true })
        .eq("user_id", user.id);

      setUserStats({
        points: profile?.points || 0,
        level: profile?.level || 1,
        ratingsCount: ratingsCount || 0,
      });
    } catch (error) {
      console.error("Error fetching user stats:", error);
    }
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Trophy className="h-5 w-5 text-yellow-500" />;
      case 2:
        return <Medal className="h-5 w-5 text-gray-400" />;
      case 3:
        return <Award className="h-5 w-5 text-amber-600" />;
      default:
        return <span className="w-5 h-5 flex items-center justify-center text-sm font-bold">{rank}</span>;
    }
  };

  const getRankBadge = (rank: number) => {
    if (rank <= 3) {
      const colors = {
        1: "bg-yellow-500",
        2: "bg-gray-400", 
        3: "bg-amber-600"
      };
      return `${colors[rank as keyof typeof colors]} text-white`;
    }
    return "bg-muted text-muted-foreground";
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Trophy className="h-5 w-5" />
            <span>Leaderboard</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {leaders.length === 0 ? (
            <p className="text-muted-foreground text-center py-8">
              No users on the leaderboard yet. Be the first to start rating!
            </p>
          ) : (
            <div className="space-y-3">
              {leaders.map((leader, index) => {
                const rank = index + 1;
                return (
                  <div
                    key={leader.id}
                    className="flex items-center space-x-4 p-3 rounded-lg border"
                  >
                    <Badge variant="secondary" className={getRankBadge(rank)}>
                      {getRankIcon(rank)}
                    </Badge>
                    
                    <div className="flex-1">
                      <div className="font-semibold">
                        {leader.display_name || leader.username}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        @{leader.username}
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className="font-bold text-primary">
                        {leader.points} pts
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Level {leader.level}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};