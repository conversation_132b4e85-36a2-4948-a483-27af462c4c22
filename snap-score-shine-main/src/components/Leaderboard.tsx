import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Trophy, Medal, Award } from "lucide-react";

interface LeaderboardEntry {
  id: string;
  username: string;
  display_name: string;
  points: number;
  level: number;
}

export const Leaderboard = () => {
  const [leaders, setLeaders] = useState<LeaderboardEntry[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchLeaderboard();
  }, []);

  const fetchLeaderboard = async () => {
    try {
      const { data, error } = await supabase
        .from("profiles")
        .select("id, username, display_name, points, level")
        .order("points", { ascending: false })
        .limit(10);

      if (error) throw error;
      setLeaders(data || []);
    } catch (error) {
      console.error("Error fetching leaderboard:", error);
    } finally {
      setLoading(false);
    }
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Trophy className="h-5 w-5 text-yellow-500" />;
      case 2:
        return <Medal className="h-5 w-5 text-gray-400" />;
      case 3:
        return <Award className="h-5 w-5 text-amber-600" />;
      default:
        return <span className="w-5 h-5 flex items-center justify-center text-sm font-bold">{rank}</span>;
    }
  };

  const getRankBadge = (rank: number) => {
    if (rank <= 3) {
      const colors = {
        1: "bg-yellow-500",
        2: "bg-gray-400", 
        3: "bg-amber-600"
      };
      return `${colors[rank as keyof typeof colors]} text-white`;
    }
    return "bg-muted text-muted-foreground";
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Trophy className="h-5 w-5" />
            <span>Leaderboard</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {leaders.length === 0 ? (
            <p className="text-muted-foreground text-center py-8">
              No users on the leaderboard yet. Be the first to start rating!
            </p>
          ) : (
            <div className="space-y-3">
              {leaders.map((leader, index) => {
                const rank = index + 1;
                return (
                  <div
                    key={leader.id}
                    className="flex items-center space-x-4 p-3 rounded-lg border"
                  >
                    <Badge variant="secondary" className={getRankBadge(rank)}>
                      {getRankIcon(rank)}
                    </Badge>
                    
                    <div className="flex-1">
                      <div className="font-semibold">
                        {leader.display_name || leader.username}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        @{leader.username}
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className="font-bold text-primary">
                        {leader.points} pts
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Level {leader.level}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};