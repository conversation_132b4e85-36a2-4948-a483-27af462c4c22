import { useAuth } from "@/hooks/useAuth";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { LogOut, Camera } from "lucide-react";

export const Navigation = () => {
  const { signOut } = useAuth();

  return (
    <nav className="border-b bg-card/50 backdrop-blur-sm">
      <div className="container mx-auto px-4 h-16 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Camera className="h-6 w-6 text-primary" />
          <h1 className="text-xl font-bold">RateDate</h1>
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={signOut}
          className="flex items-center space-x-2"
        >
          <LogOut className="h-4 w-4" />
          <span>Sign Out</span>
        </Button>
      </div>
    </nav>
  );
};