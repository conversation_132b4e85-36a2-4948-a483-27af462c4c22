import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/hooks/useAuth";
import { User, Trophy, Star, Camera } from "lucide-react";

interface ProfileData {
  id: string;
  username: string;
  display_name: string;
  avatar_url: string | null;
  bio: string | null;
  points: number;
  level: number;
}

interface UserBadge {
  id: string;
  badges: {
    name: string;
    description: string;
    icon: string;
  };
  earned_at: string;
}

export const Profile = () => {
  const [profile, setProfile] = useState<ProfileData | null>(null);
  const [badges, setBadges] = useState<UserBadge[]>([]);
  const [userPhotos, setUserPhotos] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  useEffect(() => {
    if (user) {
      fetchProfile();
      fetchBadges();
      fetchUserPhotos();
    }
  }, [user]);

  const fetchProfile = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from("profiles")
        .select("*")
        .eq("user_id", user.id)
        .single();

      if (error) throw error;
      setProfile(data);
    } catch (error) {
      console.error("Error fetching profile:", error);
    }
  };

  const fetchBadges = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from("user_badges")
        .select(`
          id,
          earned_at,
          badges (
            name,
            description,
            icon
          )
        `)
        .eq("user_id", user.id);

      if (error) throw error;
      setBadges(data || []);
    } catch (error) {
      console.error("Error fetching badges:", error);
    }
  };

  const fetchUserPhotos = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from("photos")
        .select("id, url, caption, total_ratings, average_rating")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false });

      if (error) throw error;
      setUserPhotos(data || []);
    } catch (error) {
      console.error("Error fetching user photos:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Profile Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <User className="h-5 w-5" />
            <span>Profile</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center">
              {profile?.avatar_url ? (
                <img
                  src={profile.avatar_url}
                  alt="Avatar"
                  className="w-full h-full rounded-full object-cover"
                />
              ) : (
                <User className="h-8 w-8 text-muted-foreground" />
              )}
            </div>
            <div>
              <h2 className="text-xl font-bold">
                {profile?.display_name || profile?.username}
              </h2>
              <p className="text-muted-foreground">@{profile?.username}</p>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-primary">{profile?.points || 0}</div>
              <div className="text-sm text-muted-foreground">Points</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-primary">{profile?.level || 1}</div>
              <div className="text-sm text-muted-foreground">Level</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-primary">{userPhotos.length}</div>
              <div className="text-sm text-muted-foreground">Photos</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Badges */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Trophy className="h-5 w-5" />
            <span>Badges</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {badges.length === 0 ? (
            <p className="text-muted-foreground text-center py-4">
              No badges earned yet. Keep rating photos to earn your first badge!
            </p>
          ) : (
            <div className="grid grid-cols-2 gap-3">
              {badges.map((badge) => (
                <Badge
                  key={badge.id}
                  variant="secondary"
                  className="p-3 h-auto flex flex-col items-center space-y-1"
                >
                  <span className="text-lg">{badge.badges.icon}</span>
                  <span className="font-semibold">{badge.badges.name}</span>
                  <span className="text-xs text-center">{badge.badges.description}</span>
                </Badge>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* User Photos */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Camera className="h-5 w-5" />
            <span>My Photos</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {userPhotos.length === 0 ? (
            <p className="text-muted-foreground text-center py-4">
              No photos uploaded yet. Upload your first photo to get started!
            </p>
          ) : (
            <div className="grid grid-cols-2 gap-4">
              {userPhotos.map((photo) => (
                <div key={photo.id} className="space-y-2">
                  <img
                    src={photo.url}
                    alt={photo.caption || "Photo"}
                    className="w-full aspect-square object-cover rounded-lg"
                  />
                  <div className="text-sm">
                    <div className="flex items-center space-x-2">
                      <Star className="h-3 w-3 text-yellow-500" />
                      <span>
                        {photo.average_rating 
                          ? `${photo.average_rating}/10` 
                          : "No ratings yet"
                        }
                      </span>
                    </div>
                    <div className="text-muted-foreground">
                      {photo.total_ratings} rating{photo.total_ratings !== 1 ? 's' : ''}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};