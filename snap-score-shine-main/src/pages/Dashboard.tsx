import { useAuth } from "@/hooks/useAuth";
import { Navigate } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { PhotoSwiper } from "@/components/PhotoSwiper";
import { Profile } from "@/components/Profile";
import { Leaderboard } from "@/components/Leaderboard";
import { PhotoUpload } from "@/components/PhotoUpload";
import { Navigation } from "@/components/Navigation";

const Dashboard = () => {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/auth" replace />;
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="container mx-auto px-4 py-6">
        <Tabs defaultValue="rate" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="rate">Rate</TabsTrigger>
            <TabsTrigger value="upload">Upload</TabsTrigger>
            <TabsTrigger value="leaderboard">Leaderboard</TabsTrigger>
            <TabsTrigger value="profile">Profile</TabsTrigger>
          </TabsList>

          <TabsContent value="rate" className="mt-6">
            <PhotoSwiper />
          </TabsContent>

          <TabsContent value="upload" className="mt-6">
            <PhotoUpload />
          </TabsContent>

          <TabsContent value="leaderboard" className="mt-6">
            <Leaderboard />
          </TabsContent>

          <TabsContent value="profile" className="mt-6">
            <Profile />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Dashboard;