-- Add foreign key relationship between photos and profiles
-- This will allow us to use the !inner syntax for joins

-- First, let's add a foreign key constraint from photos to profiles
-- We need to reference profiles.user_id since that's what links to auth.users

-- Add a foreign key constraint to enable proper joins
ALTER TABLE public.photos 
ADD CONSTRAINT photos_user_id_profiles_fkey 
FOREIGN KEY (user_id) REFERENCES public.profiles(user_id) ON DELETE CASCADE;

-- Create an index for better performance on joins
CREATE INDEX IF NOT EXISTS idx_photos_user_id ON public.photos(user_id);
CREATE INDEX IF NOT EXISTS idx_profiles_user_id ON public.profiles(user_id);

-- Update the RLS policies to be more specific if needed
-- (The existing policies should work fine, but we can optimize them later)

-- Add some helpful views for common queries
CREATE OR REPLACE VIEW photos_with_profiles AS
SELECT 
  p.id,
  p.url,
  p.caption,
  p.user_id,
  p.total_ratings,
  p.average_rating,
  p.created_at,
  p.updated_at,
  pr.username,
  pr.display_name,
  pr.avatar_url,
  pr.points,
  pr.level
FROM public.photos p
LEFT JOIN public.profiles pr ON p.user_id = pr.user_id;

-- Grant access to the view
GRANT SELECT ON photos_with_profiles TO authenticated;
GRANT SELECT ON photos_with_profiles TO anon;
