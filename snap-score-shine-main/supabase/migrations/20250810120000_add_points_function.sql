-- Create function to increment user points
CREATE OR REPLACE FUNCTION increment_user_points(user_id UUID, points_to_add INTEGER)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE public.profiles 
  SET points = points + points_to_add
  WHERE profiles.user_id = increment_user_points.user_id;
END;
$$;

-- Create function to update photo ratings
CREATE OR REPLACE FUNCTION update_photo_rating(photo_id UUID, new_rating INTEGER)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_total INTEGER;
  current_average DECIMAL(3,2);
  new_total INTEGER;
  new_average DECIMAL(3,2);
BEGIN
  -- Get current values
  SELECT total_ratings, average_rating 
  INTO current_total, current_average
  FROM public.photos 
  WHERE id = photo_id;
  
  -- Calculate new values
  new_total := current_total + 1;
  new_average := ((current_average * current_total) + new_rating) / new_total;
  
  -- Update photo
  UPDATE public.photos 
  SET 
    total_ratings = new_total,
    average_rating = new_average
  WHERE id = photo_id;
END;
$$;
