# 📸 Snap Score Shine - Testing Guide

## 🚀 Quick Start Testing

### Prerequisites
- App is running at `http://localhost:8080`
- Supabase database is connected and configured

### Test Accounts Available
1. **Test User 1**: `<EMAIL>` / `testpassword123`
2. **Existing User**: `dexter` (already has 1 photo uploaded)

## 🧪 Testing Scenarios

### 1. Authentication Testing
- [ ] **Sign Up**: Create a new account with email/password
- [ ] **Sign In**: Log in with existing credentials
- [ ] **Social Auth**: Test Google/GitHub login (if configured)
- [ ] **Sign Out**: Verify logout functionality
- [ ] **Profile Creation**: Check if profile is auto-created on signup

### 2. Photo Upload Testing
- [ ] **Upload Photo**: Use the Upload tab to add a new photo
- [ ] **Add Caption**: Test photo with and without captions
- [ ] **File Validation**: Try uploading non-image files
- [ ] **Storage**: Verify photos are stored in Supabase storage
- [ ] **Database**: Check if photo records are created in database

### 3. Photo Rating System Testing
- [ ] **View Photos**: Navigate to Rate tab and see photos from other users
- [ ] **Rate Photos**: Click rating buttons (1-10) to rate photos
- [ ] **Points System**: Verify points are awarded based on accuracy
- [ ] **Visual Feedback**: Check for animations and feedback on rating
- [ ] **No Self-Rating**: Ensure users can't rate their own photos
- [ ] **No Duplicate Rating**: Verify users can't rate the same photo twice

### 4. Scoring Algorithm Testing
- [ ] **First Rating**: Rate a photo that has no previous ratings (should get base points)
- [ ] **Accurate Rating**: Rate close to existing average (should get more points)
- [ ] **Inaccurate Rating**: Rate far from average (should get fewer points)
- [ ] **Points Display**: Check if earned points are shown in feedback

### 5. Gamification Features Testing
- [ ] **Leaderboard**: Check if users appear on leaderboard with correct points
- [ ] **Badges**: Verify badge system works (First Rating, Accurate Judge, etc.)
- [ ] **Levels**: Check if user levels increase with points
- [ ] **Achievements**: Test achievement notifications

### 6. UI/UX Testing
- [ ] **Mobile Responsive**: Test on different screen sizes
- [ ] **Animations**: Verify smooth transitions and animations
- [ ] **Loading States**: Check loading indicators work properly
- [ ] **Error Handling**: Test error messages for failed operations
- [ ] **Navigation**: Test tab navigation between Rate/Upload/Leaderboard/Profile

### 7. Profile Management Testing
- [ ] **View Profile**: Check profile information display
- [ ] **Edit Profile**: Test profile editing functionality
- [ ] **Avatar Upload**: Test avatar image upload
- [ ] **Stats Display**: Verify user stats (points, level, ratings count)

## 🔧 Manual Testing Steps

### Step 1: Create Test Users
```bash
# Run this to create additional test users
node scripts/create-test-user.js
```

### Step 2: Upload Test Photos
1. Sign in as User 1
2. Go to Upload tab
3. Upload 2-3 photos with different captions
4. Sign out

### Step 3: Test Rating System
1. Sign in as User 2
2. Go to Rate tab
3. Rate the photos uploaded by User 1
4. Observe points earned and feedback animations
5. Check leaderboard for updated scores

### Step 4: Test Cross-User Interaction
1. Sign in as User 1 again
2. Upload more photos
3. Check if User 2's ratings affected the averages
4. Rate User 2's photos (if any)

## 🐛 Common Issues to Check

### Database Issues
- [ ] RLS policies allow proper data access
- [ ] Foreign key constraints work correctly
- [ ] Triggers fire properly (profile creation, point updates)

### Storage Issues
- [ ] Photos upload to correct bucket
- [ ] Public URLs are accessible
- [ ] File permissions are correct

### Authentication Issues
- [ ] JWT tokens are valid
- [ ] Session persistence works
- [ ] User context is maintained across tabs

### Performance Issues
- [ ] Photo loading is fast
- [ ] Database queries are optimized
- [ ] No memory leaks in animations

## 📊 Expected Results

### After Successful Testing:
1. **Users**: Multiple user profiles in database
2. **Photos**: Several photos with varying ratings
3. **Ratings**: Rating records with calculated points
4. **Leaderboard**: Users ranked by points
5. **Badges**: Some users should have earned badges
6. **Smooth UX**: All animations and transitions work

## 🚨 Troubleshooting

### If Photos Don't Load:
- Check Supabase storage bucket permissions
- Verify public URL generation
- Check network requests in browser dev tools

### If Ratings Don't Work:
- Check RLS policies on ratings table
- Verify user authentication state
- Check for JavaScript errors in console

### If Points Don't Update:
- Check if increment_user_points function exists
- Verify profile table updates
- Check for database constraint violations

## 📈 Performance Monitoring

Monitor these metrics during testing:
- Page load times
- Image loading speeds
- Database query response times
- Animation frame rates
- Memory usage

## ✅ Test Completion Checklist

- [ ] All authentication flows work
- [ ] Photo upload and storage work
- [ ] Rating system calculates points correctly
- [ ] Leaderboard updates properly
- [ ] Badges are awarded correctly
- [ ] UI is responsive and smooth
- [ ] No console errors
- [ ] All user interactions work as expected

---

## 🎯 Next Steps After Testing

1. **Fix any bugs** found during testing
2. **Optimize performance** bottlenecks
3. **Enhance UI/UX** based on testing feedback
4. **Add admin panel** for content moderation
5. **Deploy to production** environment
