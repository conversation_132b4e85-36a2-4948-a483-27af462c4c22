import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://tzfoxpgxtcnetifykhez.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR6Zm94cGd4dGNuZXRpZnlraGV6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ4MjIyODQsImV4cCI6MjA3MDM5ODI4NH0.SDotFkXJpM2gSrJn5JnlO17KMeQlburn0V_kFjA5Ag4";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testRatingSystem() {
  console.log('🧪 Testing the complete rating system...');

  try {
    // 1. Check if we have photos to rate
    console.log('\n1. Checking available photos...');
    const { data: photos, error: photosError } = await supabase
      .from('photos')
      .select('*')
      .limit(5);

    if (photosError) {
      console.error('❌ Error fetching photos:', photosError);
      return;
    }

    console.log(`✅ Found ${photos.length} photos in database`);
    if (photos.length === 0) {
      console.log('⚠️  No photos available for rating. Upload some photos first!');
      return;
    }

    // 2. Check profiles
    console.log('\n2. Checking user profiles...');
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .limit(5);

    if (profilesError) {
      console.error('❌ Error fetching profiles:', profilesError);
      return;
    }

    console.log(`✅ Found ${profiles.length} user profiles`);
    profiles.forEach(profile => {
      console.log(`   - ${profile.display_name || profile.username}: ${profile.points} points, Level ${profile.level}`);
    });

    // 3. Check existing ratings
    console.log('\n3. Checking existing ratings...');
    const { data: ratings, error: ratingsError } = await supabase
      .from('ratings')
      .select('*')
      .limit(10);

    if (ratingsError) {
      console.error('❌ Error fetching ratings:', ratingsError);
      return;
    }

    console.log(`✅ Found ${ratings.length} existing ratings`);
    if (ratings.length > 0) {
      console.log('   Sample ratings:');
      ratings.slice(0, 3).forEach(rating => {
        console.log(`   - Rating: ${rating.rating}/10, Points earned: ${rating.points_earned}`);
      });
    }

    // 4. Test the scoring algorithm
    console.log('\n4. Testing scoring algorithm...');
    
    function calculatePoints(userRating, averageRating, totalRatings) {
      if (totalRatings === 0) return 5; // Base points for first rating
      
      const difference = Math.abs(userRating - averageRating);
      const maxPoints = 20;
      const minPoints = 1;
      
      // More points for being closer to the average
      const accuracy = Math.max(0, 1 - (difference / 9)); // 9 is max possible difference
      const points = Math.round(minPoints + (maxPoints - minPoints) * accuracy);
      
      return points;
    }

    // Test different scenarios
    const testCases = [
      { userRating: 8, averageRating: 0, totalRatings: 0, description: "First rating" },
      { userRating: 8, averageRating: 8, totalRatings: 5, description: "Perfect match" },
      { userRating: 7, averageRating: 8, totalRatings: 5, description: "Close match" },
      { userRating: 5, averageRating: 8, totalRatings: 5, description: "Moderate difference" },
      { userRating: 2, averageRating: 8, totalRatings: 5, description: "Large difference" },
    ];

    testCases.forEach(testCase => {
      const points = calculatePoints(testCase.userRating, testCase.averageRating, testCase.totalRatings);
      console.log(`   ${testCase.description}: ${points} points`);
    });

    // 5. Check badges system
    console.log('\n5. Checking badges system...');
    const { data: badges, error: badgesError } = await supabase
      .from('badges')
      .select('*');

    if (badgesError) {
      console.error('❌ Error fetching badges:', badgesError);
      return;
    }

    console.log(`✅ Found ${badges.length} available badges:`);
    badges.forEach(badge => {
      console.log(`   ${badge.icon} ${badge.name}: ${badge.description} (${badge.points_required} points)`);
    });

    // 6. Check user badges
    console.log('\n6. Checking earned badges...');
    const { data: userBadges, error: userBadgesError } = await supabase
      .from('user_badges')
      .select(`
        *,
        badges (name, icon, description)
      `);

    if (userBadgesError) {
      console.error('❌ Error fetching user badges:', userBadgesError);
    } else {
      console.log(`✅ Found ${userBadges.length} earned badges`);
      if (userBadges.length > 0) {
        userBadges.forEach(userBadge => {
          console.log(`   ${userBadge.badges.icon} ${userBadge.badges.name}`);
        });
      }
    }

    // 7. Summary and recommendations
    console.log('\n🎯 System Status Summary:');
    console.log(`   📸 Photos: ${photos.length} available`);
    console.log(`   👥 Users: ${profiles.length} registered`);
    console.log(`   ⭐ Ratings: ${ratings.length} submitted`);
    console.log(`   🏆 Badges: ${badges.length} available, ${userBadges.length} earned`);

    console.log('\n📋 Testing Recommendations:');
    if (photos.length < 3) {
      console.log('   📸 Upload more photos to test the rating system');
    }
    if (profiles.length < 2) {
      console.log('   👥 Create more user accounts to test cross-user rating');
    }
    if (ratings.length === 0) {
      console.log('   ⭐ Submit some ratings to test the points system');
    }

    console.log('\n🚀 Ready for manual testing at: http://localhost:8080');
    console.log('   Use <EMAIL> / testpassword123 to sign in');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testRatingSystem();
