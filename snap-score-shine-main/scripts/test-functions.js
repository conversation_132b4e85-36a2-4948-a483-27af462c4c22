import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://tzfoxpgxtcnetifykhez.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR6Zm94cGd4dGNuZXRpZnlraGV6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ4MjIyODQsImV4cCI6MjA3MDM5ODI4NH0.SDotFkXJpM2gSrJn5JnlO17KMeQlburn0V_kFjA5Ag4";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testDatabaseFunctions() {
  console.log('Testing database functions...');

  try {
    // Test if increment_user_points function exists
    console.log('1. Testing increment_user_points function...');
    
    // Get a test user ID
    const { data: profiles } = await supabase
      .from('profiles')
      .select('user_id')
      .limit(1);

    if (profiles && profiles.length > 0) {
      const testUserId = profiles[0].user_id;
      
      // Try to call the function
      const { data, error } = await supabase.rpc('increment_user_points', {
        user_id: testUserId,
        points_to_add: 0  // Add 0 points just to test the function
      });

      if (error) {
        console.error('❌ increment_user_points function error:', error);
        console.log('   This function needs to be created in the database');
      } else {
        console.log('✅ increment_user_points function works');
      }
    } else {
      console.log('⚠️  No users found to test with');
    }

    // Test basic database operations
    console.log('\n2. Testing basic operations...');
    
    // Test if we can read from all tables
    const tables = ['profiles', 'photos', 'ratings', 'badges', 'user_badges'];
    
    for (const table of tables) {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);
        
      if (error) {
        console.error(`❌ Error reading from ${table}:`, error);
      } else {
        console.log(`✅ ${table} table accessible`);
      }
    }

    console.log('\n🎉 Database function test complete!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testDatabaseFunctions();
