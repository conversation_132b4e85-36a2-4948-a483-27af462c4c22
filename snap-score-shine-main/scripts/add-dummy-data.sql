-- Insert dummy users (these would normally be created through auth.users)
-- Note: In a real scenario, users are created through Supabase Auth
-- For testing, we'll insert directly into profiles table

-- First, let's insert some test profiles
INSERT INTO public.profiles (id, user_id, username, display_name, points, level) VALUES
('550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', 'alice_photo', '<PERSON>', 150, 2),
('550e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440002', 'bob_snap', '<PERSON>', 89, 1),
('550e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440003', 'carol_pics', '<PERSON>', 234, 3),
('550e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440004', 'david_shots', '<PERSON>', 67, 1),
('550e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440005', 'emma_lens', 'Emma Brown', 312, 4)
ON CONFLICT (user_id) DO NOTHING;

-- Insert some dummy photos with placeholder URLs
INSERT INTO public.photos (id, user_id, url, caption, total_ratings, average_rating) VALUES
('660e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', 'https://picsum.photos/400/400?random=1', 'Beautiful sunset at the beach', 5, 7.2),
('660e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440001', 'https://picsum.photos/400/400?random=2', 'City skyline at night', 3, 8.1),
('660e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440002', 'https://picsum.photos/400/400?random=3', 'Mountain hiking adventure', 7, 6.8),
('660e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440002', 'https://picsum.photos/400/400?random=4', 'Coffee and morning vibes', 2, 9.0),
('660e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440003', 'https://picsum.photos/400/400?random=5', 'Street art discovery', 4, 7.5),
('660e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-446655440003', 'https://picsum.photos/400/400?random=6', 'Garden flowers in bloom', 6, 8.3),
('660e8400-e29b-41d4-a716-446655440007', '550e8400-e29b-41d4-a716-446655440004', 'https://picsum.photos/400/400?random=7', 'Concert night energy', 1, 6.0),
('660e8400-e29b-41d4-a716-446655440008', '550e8400-e29b-41d4-a716-446655440004', 'https://picsum.photos/400/400?random=8', 'Food photography experiment', 8, 7.9),
('660e8400-e29b-41d4-a716-446655440009', '550e8400-e29b-41d4-a716-446655440005', 'https://picsum.photos/400/400?random=9', 'Beach volleyball fun', 3, 8.7),
('660e8400-e29b-41d4-a716-446655440010', '550e8400-e29b-41d4-a716-446655440005', 'https://picsum.photos/400/400?random=10', 'Winter wonderland', 5, 9.2)
ON CONFLICT (id) DO NOTHING;

-- Insert some dummy ratings
INSERT INTO public.ratings (id, user_id, photo_id, rating, points_earned) VALUES
-- Alice rating Bob's photos
('770e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440003', 7, 15),
('770e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440004', 9, 18),
-- Bob rating Carol's photos
('770e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440005', 8, 12),
('770e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440006', 8, 16),
-- Carol rating David's photos
('770e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440003', '660e8400-e29b-41d4-a716-446655440007', 6, 10),
('770e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-446655440003', '660e8400-e29b-41d4-a716-446655440008', 8, 14),
-- David rating Emma's photos
('770e8400-e29b-41d4-a716-446655440007', '550e8400-e29b-41d4-a716-446655440004', '660e8400-e29b-41d4-a716-446655440009', 9, 17),
('770e8400-e29b-41d4-a716-446655440008', '550e8400-e29b-41d4-a716-446655440004', '660e8400-e29b-41d4-a716-446655440010', 9, 19),
-- Emma rating Alice's photos
('770e8400-e29b-41d4-a716-446655440009', '550e8400-e29b-41d4-a716-446655440005', '660e8400-e29b-41d4-a716-446655440001', 7, 13),
('770e8400-e29b-41d4-a716-446655440010', '550e8400-e29b-41d4-a716-446655440005', '660e8400-e29b-41d4-a716-446655440002', 8, 16)
ON CONFLICT (user_id, photo_id) DO NOTHING;

-- Insert some badges that users can earn
INSERT INTO public.badges (id, name, description, icon, points_required) VALUES
('880e8400-e29b-41d4-a716-446655440001', 'First Rating', 'Rated your first photo', '⭐', 10),
('880e8400-e29b-41d4-a716-446655440002', 'Accurate Judge', 'Earned 100 points from accurate ratings', '🎯', 100),
('880e8400-e29b-41d4-a716-446655440003', 'Photo Critic', 'Rated 50 photos', '👁️', 250),
('880e8400-e29b-41d4-a716-446655440004', 'Eagle Eye', 'Earned 500 points from accurate ratings', '🦅', 500),
('880e8400-e29b-41d4-a716-446655440005', 'Master Rater', 'Rated 200 photos', '🏆', 1000)
ON CONFLICT (name) DO NOTHING;

-- Award some badges to users
INSERT INTO public.user_badges (id, user_id, badge_id) VALUES
('990e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', '880e8400-e29b-41d4-a716-446655440001'),
('990e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440001', '880e8400-e29b-41d4-a716-446655440002'),
('990e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440003', '880e8400-e29b-41d4-a716-446655440001'),
('990e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440003', '880e8400-e29b-41d4-a716-446655440002'),
('990e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440005', '880e8400-e29b-41d4-a716-446655440001'),
('990e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-446655440005', '880e8400-e29b-41d4-a716-446655440002'),
('990e8400-e29b-41d4-a716-446655440007', '550e8400-e29b-41d4-a716-446655440005', '880e8400-e29b-41d4-a716-446655440003')
ON CONFLICT (user_id, badge_id) DO NOTHING;
