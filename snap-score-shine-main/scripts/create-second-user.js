import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://tzfoxpgxtcnetifykhez.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR6Zm94cGd4dGNuZXRpZnlraGV6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ4MjIyODQsImV4cCI6MjA3MDM5ODI4NH0.SDotFkXJpM2gSrJn5JnlO17KMeQlburn0V_kFjA5Ag4";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function createSecondUser() {
  console.log('Creating second test user...');

  try {
    // Create a second test user
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: '<EMAIL>',
      password: 'testpassword123',
      options: {
        data: {
          username: 'alice_photos',
          display_name: '<PERSON>'
        }
      }
    });

    if (authError) {
      console.error('Error creating user:', authError);
      return;
    }

    console.log('✅ Second test user created successfully!');
    console.log('Email: <EMAIL>');
    console.log('Password: testpassword123');
    console.log('Username: alice_photos');
    
    if (authData.user) {
      console.log('User ID:', authData.user.id);
    }

    console.log('\n🎉 Second user setup complete!');
    console.log('Now you have two test users:');
    console.log('1. <EMAIL> / testpassword123');
    console.log('2. <EMAIL> / testpassword123');
    console.log('\nYou can test the rating system by:');
    console.log('1. Sign in as one user and upload photos');
    console.log('2. Sign out and sign in as the other user');
    console.log('3. Rate the photos uploaded by the first user');
    
  } catch (error) {
    console.error('Error:', error);
  }
}

createSecondUser();
