import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://tzfoxpgxtcnetifykhez.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR6Zm94cGd4dGNuZXRpZnlraGV6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ4MjIyODQsImV4cCI6MjA3MDM5ODI4NH0.SDotFkXJpM2gSrJn5JnlO17KMeQlburn0V_kFjA5Ag4";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testConnection() {
  console.log('Testing Supabase connection...');

  try {
    // Test basic connection by fetching profiles
    console.log('1. Testing profiles table...');
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .limit(5);

    if (profilesError) {
      console.error('❌ Profiles error:', profilesError);
    } else {
      console.log('✅ Profiles table accessible');
      console.log(`   Found ${profiles.length} profiles`);
      if (profiles.length > 0) {
        console.log('   Sample profile:', profiles[0]);
      }
    }

    // Test photos table
    console.log('\n2. Testing photos table...');
    const { data: photos, error: photosError } = await supabase
      .from('photos')
      .select('*')
      .limit(5);

    if (photosError) {
      console.error('❌ Photos error:', photosError);
    } else {
      console.log('✅ Photos table accessible');
      console.log(`   Found ${photos.length} photos`);
      if (photos.length > 0) {
        console.log('   Sample photo:', photos[0]);
      }
    }

    // Test badges table
    console.log('\n3. Testing badges table...');
    const { data: badges, error: badgesError } = await supabase
      .from('badges')
      .select('*')
      .limit(5);

    if (badgesError) {
      console.error('❌ Badges error:', badgesError);
    } else {
      console.log('✅ Badges table accessible');
      console.log(`   Found ${badges.length} badges`);
      if (badges.length > 0) {
        console.log('   Sample badge:', badges[0]);
      }
    }

    // Test ratings table
    console.log('\n4. Testing ratings table...');
    const { data: ratings, error: ratingsError } = await supabase
      .from('ratings')
      .select('*')
      .limit(5);

    if (ratingsError) {
      console.error('❌ Ratings error:', ratingsError);
    } else {
      console.log('✅ Ratings table accessible');
      console.log(`   Found ${ratings.length} ratings`);
      if (ratings.length > 0) {
        console.log('   Sample rating:', ratings[0]);
      }
    }

    console.log('\n🎉 Database connection test complete!');
    console.log('\nNext steps:');
    console.log('1. Go to http://localhost:8080');
    console.log('2. Sign up with a new account or sign in with: <EMAIL> / testpassword123');
    console.log('3. Upload some photos using the Upload tab');
    console.log('4. Create another account to test the rating system');
    
  } catch (error) {
    console.error('❌ Connection test failed:', error);
  }
}

testConnection();
