import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://tzfoxpgxtcnetifykhez.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR6Zm94cGd4dGNuZXRpZnlraGV6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ4MjIyODQsImV4cCI6MjA3MDM5ODI4NH0.SDotFkXJpM2gSrJn5JnlO17KMeQlburn0V_kFjA5Ag4";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function addDummyData() {
  console.log('Adding dummy data to Supabase...');

  try {
    // Insert dummy profiles
    console.log('Inserting profiles...');
    const { error: profilesError } = await supabase
      .from('profiles')
      .upsert([
        {
          id: '550e8400-e29b-41d4-a716-446655440001',
          user_id: '550e8400-e29b-41d4-a716-446655440001',
          username: 'alice_photo',
          display_name: '<PERSON>',
          points: 150,
          level: 2
        },
        {
          id: '550e8400-e29b-41d4-a716-446655440002',
          user_id: '550e8400-e29b-41d4-a716-446655440002',
          username: 'bob_snap',
          display_name: 'Bob Smith',
          points: 89,
          level: 1
        },
        {
          id: '550e8400-e29b-41d4-a716-446655440003',
          user_id: '550e8400-e29b-41d4-a716-446655440003',
          username: 'carol_pics',
          display_name: 'Carol Davis',
          points: 234,
          level: 3
        },
        {
          id: '550e8400-e29b-41d4-a716-446655440004',
          user_id: '550e8400-e29b-41d4-a716-446655440004',
          username: 'david_shots',
          display_name: 'David Wilson',
          points: 67,
          level: 1
        },
        {
          id: '550e8400-e29b-41d4-a716-446655440005',
          user_id: '550e8400-e29b-41d4-a716-446655440005',
          username: 'emma_lens',
          display_name: 'Emma Brown',
          points: 312,
          level: 4
        }
      ], { onConflict: 'user_id' });

    if (profilesError) {
      console.error('Error inserting profiles:', profilesError);
    } else {
      console.log('✅ Profiles inserted successfully');
    }

    // Insert dummy photos
    console.log('Inserting photos...');
    const { error: photosError } = await supabase
      .from('photos')
      .upsert([
        {
          id: '660e8400-e29b-41d4-a716-446655440001',
          user_id: '550e8400-e29b-41d4-a716-446655440001',
          url: 'https://picsum.photos/400/400?random=1',
          caption: 'Beautiful sunset at the beach',
          total_ratings: 5,
          average_rating: 7.2
        },
        {
          id: '660e8400-e29b-41d4-a716-446655440002',
          user_id: '550e8400-e29b-41d4-a716-446655440001',
          url: 'https://picsum.photos/400/400?random=2',
          caption: 'City skyline at night',
          total_ratings: 3,
          average_rating: 8.1
        },
        {
          id: '660e8400-e29b-41d4-a716-446655440003',
          user_id: '550e8400-e29b-41d4-a716-446655440002',
          url: 'https://picsum.photos/400/400?random=3',
          caption: 'Mountain hiking adventure',
          total_ratings: 7,
          average_rating: 6.8
        },
        {
          id: '660e8400-e29b-41d4-a716-446655440004',
          user_id: '550e8400-e29b-41d4-a716-446655440002',
          url: 'https://picsum.photos/400/400?random=4',
          caption: 'Coffee and morning vibes',
          total_ratings: 2,
          average_rating: 9.0
        },
        {
          id: '660e8400-e29b-41d4-a716-446655440005',
          user_id: '550e8400-e29b-41d4-a716-446655440003',
          url: 'https://picsum.photos/400/400?random=5',
          caption: 'Street art discovery',
          total_ratings: 4,
          average_rating: 7.5
        },
        {
          id: '660e8400-e29b-41d4-a716-446655440006',
          user_id: '550e8400-e29b-41d4-a716-446655440003',
          url: 'https://picsum.photos/400/400?random=6',
          caption: 'Garden flowers in bloom',
          total_ratings: 6,
          average_rating: 8.3
        },
        {
          id: '660e8400-e29b-41d4-a716-446655440007',
          user_id: '550e8400-e29b-41d4-a716-446655440004',
          url: 'https://picsum.photos/400/400?random=7',
          caption: 'Concert night energy',
          total_ratings: 1,
          average_rating: 6.0
        },
        {
          id: '660e8400-e29b-41d4-a716-446655440008',
          user_id: '550e8400-e29b-41d4-a716-446655440004',
          url: 'https://picsum.photos/400/400?random=8',
          caption: 'Food photography experiment',
          total_ratings: 8,
          average_rating: 7.9
        },
        {
          id: '660e8400-e29b-41d4-a716-446655440009',
          user_id: '550e8400-e29b-41d4-a716-446655440005',
          url: 'https://picsum.photos/400/400?random=9',
          caption: 'Beach volleyball fun',
          total_ratings: 3,
          average_rating: 8.7
        },
        {
          id: '660e8400-e29b-41d4-a716-446655440010',
          user_id: '550e8400-e29b-41d4-a716-446655440005',
          url: 'https://picsum.photos/400/400?random=10',
          caption: 'Winter wonderland',
          total_ratings: 5,
          average_rating: 9.2
        }
      ], { onConflict: 'id' });

    if (photosError) {
      console.error('Error inserting photos:', photosError);
    } else {
      console.log('✅ Photos inserted successfully');
    }

    console.log('🎉 Dummy data added successfully!');
    console.log('You can now test the app with the sample data.');
    
  } catch (error) {
    console.error('Error adding dummy data:', error);
  }
}

addDummyData();
