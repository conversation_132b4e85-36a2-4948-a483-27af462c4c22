import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://tzfoxpgxtcnetifykhez.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR6Zm94cGd4dGNuZXRpZnlraGV6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ4MjIyODQsImV4cCI6MjA3MDM5ODI4NH0.SDotFkXJpM2gSrJn5JnlO17KMeQlburn0V_kFjA5Ag4";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function createTestUser() {
  console.log('Creating test user...');

  try {
    // Create a test user
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: '<EMAIL>',
      password: 'testpassword123',
      options: {
        data: {
          username: 'testuser',
          display_name: 'Test User'
        }
      }
    });

    if (authError) {
      console.error('Error creating user:', authError);
      return;
    }

    console.log('✅ Test user created successfully!');
    console.log('Email: <EMAIL>');
    console.log('Password: testpassword123');
    console.log('Username: testuser');
    
    if (authData.user) {
      console.log('User ID:', authData.user.id);
      
      // Wait a moment for the profile to be created by the trigger
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Add some test photos for this user
      console.log('Adding test photos...');
      
      const testPhotos = [
        {
          url: 'https://picsum.photos/400/400?random=101',
          caption: 'My first test photo'
        },
        {
          url: 'https://picsum.photos/400/400?random=102',
          caption: 'Another beautiful shot'
        },
        {
          url: 'https://picsum.photos/400/400?random=103',
          caption: 'Testing the photo upload'
        }
      ];

      for (const photo of testPhotos) {
        const { error: photoError } = await supabase
          .from('photos')
          .insert({
            user_id: authData.user.id,
            url: photo.url,
            caption: photo.caption
          });

        if (photoError) {
          console.error('Error adding photo:', photoError);
        } else {
          console.log(`✅ Added photo: ${photo.caption}`);
        }
      }
    }

    console.log('\n🎉 Test setup complete!');
    console.log('You can now:');
    console.log('1. Go to http://localhost:8080');
    console.log('2. Sign <NAME_EMAIL> / testpassword123');
    console.log('3. Test the photo rating functionality');
    
  } catch (error) {
    console.error('Error:', error);
  }
}

createTestUser();
