import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://tzfoxpgxtcnetifykhez.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR6Zm94cGd4dGNuZXRpZnlraGV6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ4MjIyODQsImV4cCI6MjA3MDM5ODI4NH0.SDotFkXJpM2gSrJn5JnlO17KMeQlburn0V_kFjA5Ag4";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testPhotoQuery() {
  console.log('Testing photo query with profile data...');

  try {
    // Test the new approach - get photos first
    console.log('1. Fetching photos...');
    const { data: photosData, error: photosError } = await supabase
      .from("photos")
      .select(`
        id,
        url,
        caption,
        user_id,
        total_ratings,
        average_rating
      `)
      .limit(10);

    if (photosError) {
      console.error('❌ Photos query error:', photosError);
      return;
    }

    console.log('✅ Photos fetched successfully');
    console.log(`   Found ${photosData.length} photos`);

    if (photosData && photosData.length > 0) {
      // Get profile data for all photo owners
      console.log('\n2. Fetching profile data...');
      const userIds = photosData.map(photo => photo.user_id);
      console.log('   User IDs:', userIds);

      const { data: profilesData, error: profilesError } = await supabase
        .from("profiles")
        .select("user_id, display_name, username")
        .in("user_id", userIds);

      if (profilesError) {
        console.error('❌ Profiles query error:', profilesError);
        return;
      }

      console.log('✅ Profiles fetched successfully');
      console.log(`   Found ${profilesData?.length || 0} profiles`);

      // Combine photos with profile data
      console.log('\n3. Combining data...');
      const photosWithProfiles = photosData.map(photo => ({
        ...photo,
        profiles: profilesData?.find(profile => profile.user_id === photo.user_id) || {
          display_name: "Unknown User",
          username: "unknown"
        }
      }));

      console.log('✅ Data combined successfully');
      console.log('\n📸 Sample photo with profile:');
      console.log(JSON.stringify(photosWithProfiles[0], null, 2));

      console.log('\n🎉 Photo query test successful!');
      console.log('The PhotoSwiper component should now work correctly.');
    } else {
      console.log('⚠️  No photos found in database');
      console.log('   Upload some photos through the app to test the rating system');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testPhotoQuery();
