{"hash": "5f04c5c9", "configHash": "6f2d9c40", "lockfileHash": "78eb86f2", "browserHash": "bd985350", "optimized": {"react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "ec63a04b", "needsInterop": true}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "0a081e49", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "3d4d6491", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "ce914d5f", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "04d7426c", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "8270d988", "needsInterop": false}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "63e52021", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "98f3a068", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "a76827a2", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "0d9a3028", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "f58bb2fd", "needsInterop": false}, "next-themes": {"src": "../../next-themes/dist/index.mjs", "file": "next-themes.js", "fileHash": "8fc2eae6", "needsInterop": false}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "5121a913", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "ae4e03b6", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "b1355a2e", "needsInterop": false}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "70a02ed3", "needsInterop": true}, "sonner": {"src": "../../sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "bab1d25b", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "c6ab07ec", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "67fc3f65", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "a4952904", "needsInterop": false}}, "chunks": {"browser-25TXITVJ": {"file": "browser-25TXITVJ.js"}, "chunk-XYJZDNPW": {"file": "chunk-XYJZDNPW.js"}, "chunk-OGDCXL4J": {"file": "chunk-OGDCXL4J.js"}, "chunk-C73GY55N": {"file": "chunk-C73GY55N.js"}, "chunk-GQO6HEXE": {"file": "chunk-GQO6HEXE.js"}, "chunk-UZBY6SFG": {"file": "chunk-UZBY6SFG.js"}, "chunk-7EOIXRG6": {"file": "chunk-7EOIXRG6.js"}, "chunk-W3FHAHPV": {"file": "chunk-W3FHAHPV.js"}, "chunk-ABAMWORX": {"file": "chunk-ABAMWORX.js"}, "chunk-42XBNZVG": {"file": "chunk-42XBNZVG.js"}, "chunk-TQGXHKBZ": {"file": "chunk-TQGXHKBZ.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-VHXUCOYC": {"file": "chunk-VHXUCOYC.js"}}}