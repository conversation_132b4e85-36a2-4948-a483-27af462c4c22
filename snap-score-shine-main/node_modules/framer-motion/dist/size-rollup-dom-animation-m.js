import{jsxs as t,jsx as n}from"react/jsx-runtime";import{createContext as o,useContext as r,useMemo as e,Fragment as a,createElement as i,useRef as s,useCallback as u,useLayoutEffect as c,useEffect as l,useInsertionEffect as d,forwardRef as f}from"react";import{i as m,a as p,b as y,c as g,d as v,e as h,f as S,g as M,P as b,r as w,h as j,j as C,k as E,s as P,l as A,m as T,n as L,o as W,S as x,p as I,L as k}from"./size-rollup-dom-max-assets.js";const O=o({strict:!1}),V=o({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),D=o({});function R(t){const{initial:n,animate:o}=function(t,n){if(m(t)){const{initial:n,animate:o}=t;return{initial:!1===n||p(n)?n:void 0,animate:p(o)?o:void 0}}return!1!==t.inherit?n:{}}(t,r(D));return e(()=>({initial:n,animate:o}),[H(n),H(o)])}function H(t){return Array.isArray(t)?t.join(" "):t}const N=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function F(t,n,o){for(const r in n)y(n[r])||g(r,o)||(t[r]=n[r])}function B(t,n){const o={};return F(o,t.style||{},t),Object.assign(o,function({transformTemplate:t},n){return e(()=>{const o={style:{},transform:{},transformOrigin:{},vars:{}};return v(o,n,t),Object.assign({},o.vars,o.style)},[n])}(t,n)),o}function q(t,n){const o={},r=B(t,n);return t.drag&&!1!==t.dragListener&&(o.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(o.tabIndex=0),o.style=r,o}const U=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}});function $(t,n,o,r){const a=e(()=>{const o={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return h(o,n,S(r),t.transformTemplate,t.style),{...o.attrs,style:{...o.style}}},[n]);if(t.style){const n={};F(n,t.style,t),a.style={...n,...a.style}}return a}const _=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function z(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||_.has(t)}let X=t=>!z(t);try{"function"==typeof(Y=require("@emotion/is-prop-valid").default)&&(X=t=>t.startsWith("on")?!z(t):Y(t))}catch{}var Y;function G(t,n,o,{latestValues:r},s,u=!1){const c=(M(t)?$:q)(n,r,s,t),l=function(t,n,o){const r={};for(const e in t)"values"===e&&"object"==typeof t.values||(X(e)||!0===o&&z(e)||!n&&!z(e)||t.draggable&&e.startsWith("onDrag"))&&(r[e]=t[e]);return r}(n,"string"==typeof t,u),d=t!==a?{...l,...c,ref:o}:{},{children:f}=n,m=e(()=>y(f)?f.get():f,[f]);return i(t,{...d,children:m})}function J(t,n,o,r){const e={},a=r(t,{});for(const t in a)e[t]=w(a[t]);let{initial:i,animate:s}=t;const u=m(t),c=j(t);n&&c&&!u&&!1!==t.inherit&&(void 0===i&&(i=n.initial),void 0===s&&(s=n.animate));let l=!!o&&!1===o.initial;l=l||!1===i;const d=l?s:i;if(d&&"boolean"!=typeof d&&!C(d)){const n=Array.isArray(d)?d:[d];for(let o=0;o<n.length;o++){const r=E(t,n[o]);if(r){const{transitionEnd:t,transition:n,...o}=r;for(const t in o){let n=o[t];if(Array.isArray(n)){n=n[l?n.length-1:0]}null!==n&&(e[t]=n)}for(const n in t)e[n]=t[n]}}}return e}const K=t=>(n,o)=>{const e=r(D),a=r(b),i=()=>function({scrapeMotionValuesFromProps:t,createRenderState:n},o,r,e){return{latestValues:J(o,r,e,t),renderState:n()}}(t,n,e,a);return o?i():function(t){const n=s(null);return null===n.current&&(n.current=t()),n.current}(i)},Q=K({scrapeMotionValuesFromProps:P,createRenderState:N}),Z=K({scrapeMotionValuesFromProps:A,createRenderState:U});const tt=Symbol.for("motionComponentSymbol");function nt(t,n,o){return u(r=>{r&&t.onMount&&t.onMount(r),n&&(r?n.mount(r):n.unmount()),o&&("function"==typeof o?o(r):L(o)&&(o.current=r))},[n])}const ot=W?c:l;function rt(t,n,o,e,a){const{visualElement:i}=r(D),u=r(O),c=r(b),f=r(V).reducedMotion,m=s(null);e=e||u.renderer,!m.current&&e&&(m.current=e(t,{visualState:n,parent:i,props:o,presenceContext:c,blockInitialAnimation:!!c&&!1===c.initial,reducedMotionConfig:f}));const p=m.current,y=r(x);!p||p.projection||!a||"html"!==p.type&&"svg"!==p.type||function(t,n,o,r){const{layoutId:e,layout:a,drag:i,dragConstraints:s,layoutScroll:u,layoutRoot:c,layoutCrossfade:l}=n;t.projection=new o(t.latestValues,n["data-framer-portal-id"]?void 0:et(t.parent)),t.projection.setOptions({layoutId:e,layout:a,alwaysMeasureLayout:Boolean(i)||s&&L(s),visualElement:t,animationType:"string"==typeof a?a:"both",initialPromotionConfig:r,crossfade:l,layoutScroll:u,layoutRoot:c})}(m.current,o,a,y);const g=s(!1);d(()=>{p&&g.current&&p.update(o,c)});const v=o[I],h=s(Boolean(v)&&!window.MotionHandoffIsComplete?.(v)&&window.MotionHasOptimisedAnimation?.(v));return ot(()=>{p&&(g.current=!0,window.MotionIsMounted=!0,p.updateFeatures(),p.scheduleRenderMicrotask(),h.current&&p.animationState&&p.animationState.animateChanges())}),l(()=>{p&&(!h.current&&p.animationState&&p.animationState.animateChanges(),h.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(v)}),h.current=!1),p.enteringChildren=void 0)}),p}function et(t){if(t)return!1!==t.options.allowProjection?t.projection:et(t.parent)}function at(o,{forwardMotionProps:e=!1}={},a,i){a&&function(t){for(const n in t)T[n]={...T[n],...t[n]}}(a);const s=M(o)?Z:Q;function u(a,u){let c;const l={...r(V),...a,layoutId:it(a)},{isStatic:d}=l,f=R(a),m=s(a,d);if(!d&&W){r(O).strict;const t=function(t){const{drag:n,layout:o}=T;if(!n&&!o)return{};const r={...n,...o};return{MeasureLayout:n?.isEnabled(t)||o?.isEnabled(t)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(l);c=t.MeasureLayout,f.visualElement=rt(o,m,l,i,t.ProjectionNode)}return t(D.Provider,{value:f,children:[c&&f.visualElement?n(c,{visualElement:f.visualElement,...l}):null,G(o,a,nt(m,f.visualElement,u),m,d,e)]})}u.displayName=`motion.${"string"==typeof o?o:`create(${o.displayName??o.name??""})`}`;const c=f(u);return c[tt]=o,c}function it({layoutId:t}){const n=r(k).id;return n&&void 0!==t?n+"-"+t:t}function st(t,n){return at(t,n)}const ut=st("div");export{ut as MotionDiv};
