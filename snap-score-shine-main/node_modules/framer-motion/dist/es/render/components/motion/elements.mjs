"use client";
import { createMotionComponentWithFeatures } from './create.mjs';

/**
 * HTML components
 */
const MotionA = /*@__PURE__*/ createMotionComponentWithFeatures("a");
const MotionAbbr = /*@__PURE__*/ createMotionComponentWithFeatures("abbr");
const MotionAddress = /*@__PURE__*/ createMotionComponentWithFeatures("address");
const MotionArea = /*@__PURE__*/ createMotionComponentWithFeatures("area");
const MotionArticle = /*@__PURE__*/ createMotionComponentWithFeatures("article");
const MotionAside = /*@__PURE__*/ createMotionComponentWithFeatures("aside");
const MotionAudio = /*@__PURE__*/ createMotionComponentWithFeatures("audio");
const MotionB = /*@__PURE__*/ createMotionComponentWithFeatures("b");
const MotionBase = /*@__PURE__*/ createMotionComponentWithFeatures("base");
const MotionBdi = /*@__PURE__*/ createMotionComponentWithFeatures("bdi");
const MotionBdo = /*@__PURE__*/ createMotionComponentWithFeatures("bdo");
const MotionBig = /*@__PURE__*/ createMotionComponentWithFeatures("big");
const MotionBlockquote = 
/*@__PURE__*/ createMotionComponentWithFeatures("blockquote");
const MotionBody = /*@__PURE__*/ createMotionComponentWithFeatures("body");
const MotionButton = /*@__PURE__*/ createMotionComponentWithFeatures("button");
const MotionCanvas = /*@__PURE__*/ createMotionComponentWithFeatures("canvas");
const MotionCaption = /*@__PURE__*/ createMotionComponentWithFeatures("caption");
const MotionCite = /*@__PURE__*/ createMotionComponentWithFeatures("cite");
const MotionCode = /*@__PURE__*/ createMotionComponentWithFeatures("code");
const MotionCol = /*@__PURE__*/ createMotionComponentWithFeatures("col");
const MotionColgroup = /*@__PURE__*/ createMotionComponentWithFeatures("colgroup");
const MotionData = /*@__PURE__*/ createMotionComponentWithFeatures("data");
const MotionDatalist = /*@__PURE__*/ createMotionComponentWithFeatures("datalist");
const MotionDd = /*@__PURE__*/ createMotionComponentWithFeatures("dd");
const MotionDel = /*@__PURE__*/ createMotionComponentWithFeatures("del");
const MotionDetails = /*@__PURE__*/ createMotionComponentWithFeatures("details");
const MotionDfn = /*@__PURE__*/ createMotionComponentWithFeatures("dfn");
const MotionDialog = /*@__PURE__*/ createMotionComponentWithFeatures("dialog");
const MotionDiv = /*@__PURE__*/ createMotionComponentWithFeatures("div");
const MotionDl = /*@__PURE__*/ createMotionComponentWithFeatures("dl");
const MotionDt = /*@__PURE__*/ createMotionComponentWithFeatures("dt");
const MotionEm = /*@__PURE__*/ createMotionComponentWithFeatures("em");
const MotionEmbed = /*@__PURE__*/ createMotionComponentWithFeatures("embed");
const MotionFieldset = /*@__PURE__*/ createMotionComponentWithFeatures("fieldset");
const MotionFigcaption = 
/*@__PURE__*/ createMotionComponentWithFeatures("figcaption");
const MotionFigure = /*@__PURE__*/ createMotionComponentWithFeatures("figure");
const MotionFooter = /*@__PURE__*/ createMotionComponentWithFeatures("footer");
const MotionForm = /*@__PURE__*/ createMotionComponentWithFeatures("form");
const MotionH1 = /*@__PURE__*/ createMotionComponentWithFeatures("h1");
const MotionH2 = /*@__PURE__*/ createMotionComponentWithFeatures("h2");
const MotionH3 = /*@__PURE__*/ createMotionComponentWithFeatures("h3");
const MotionH4 = /*@__PURE__*/ createMotionComponentWithFeatures("h4");
const MotionH5 = /*@__PURE__*/ createMotionComponentWithFeatures("h5");
const MotionH6 = /*@__PURE__*/ createMotionComponentWithFeatures("h6");
const MotionHead = /*@__PURE__*/ createMotionComponentWithFeatures("head");
const MotionHeader = /*@__PURE__*/ createMotionComponentWithFeatures("header");
const MotionHgroup = /*@__PURE__*/ createMotionComponentWithFeatures("hgroup");
const MotionHr = /*@__PURE__*/ createMotionComponentWithFeatures("hr");
const MotionHtml = /*@__PURE__*/ createMotionComponentWithFeatures("html");
const MotionI = /*@__PURE__*/ createMotionComponentWithFeatures("i");
const MotionIframe = /*@__PURE__*/ createMotionComponentWithFeatures("iframe");
const MotionImg = /*@__PURE__*/ createMotionComponentWithFeatures("img");
const MotionInput = /*@__PURE__*/ createMotionComponentWithFeatures("input");
const MotionIns = /*@__PURE__*/ createMotionComponentWithFeatures("ins");
const MotionKbd = /*@__PURE__*/ createMotionComponentWithFeatures("kbd");
const MotionKeygen = /*@__PURE__*/ createMotionComponentWithFeatures("keygen");
const MotionLabel = /*@__PURE__*/ createMotionComponentWithFeatures("label");
const MotionLegend = /*@__PURE__*/ createMotionComponentWithFeatures("legend");
const MotionLi = /*@__PURE__*/ createMotionComponentWithFeatures("li");
const MotionLink = /*@__PURE__*/ createMotionComponentWithFeatures("link");
const MotionMain = /*@__PURE__*/ createMotionComponentWithFeatures("main");
const MotionMap = /*@__PURE__*/ createMotionComponentWithFeatures("map");
const MotionMark = /*@__PURE__*/ createMotionComponentWithFeatures("mark");
const MotionMenu = /*@__PURE__*/ createMotionComponentWithFeatures("menu");
const MotionMenuitem = /*@__PURE__*/ createMotionComponentWithFeatures("menuitem");
const MotionMeter = /*@__PURE__*/ createMotionComponentWithFeatures("meter");
const MotionNav = /*@__PURE__*/ createMotionComponentWithFeatures("nav");
const MotionObject = /*@__PURE__*/ createMotionComponentWithFeatures("object");
const MotionOl = /*@__PURE__*/ createMotionComponentWithFeatures("ol");
const MotionOptgroup = /*@__PURE__*/ createMotionComponentWithFeatures("optgroup");
const MotionOption = /*@__PURE__*/ createMotionComponentWithFeatures("option");
const MotionOutput = /*@__PURE__*/ createMotionComponentWithFeatures("output");
const MotionP = /*@__PURE__*/ createMotionComponentWithFeatures("p");
const MotionParam = /*@__PURE__*/ createMotionComponentWithFeatures("param");
const MotionPicture = /*@__PURE__*/ createMotionComponentWithFeatures("picture");
const MotionPre = /*@__PURE__*/ createMotionComponentWithFeatures("pre");
const MotionProgress = /*@__PURE__*/ createMotionComponentWithFeatures("progress");
const MotionQ = /*@__PURE__*/ createMotionComponentWithFeatures("q");
const MotionRp = /*@__PURE__*/ createMotionComponentWithFeatures("rp");
const MotionRt = /*@__PURE__*/ createMotionComponentWithFeatures("rt");
const MotionRuby = /*@__PURE__*/ createMotionComponentWithFeatures("ruby");
const MotionS = /*@__PURE__*/ createMotionComponentWithFeatures("s");
const MotionSamp = /*@__PURE__*/ createMotionComponentWithFeatures("samp");
const MotionScript = /*@__PURE__*/ createMotionComponentWithFeatures("script");
const MotionSection = /*@__PURE__*/ createMotionComponentWithFeatures("section");
const MotionSelect = /*@__PURE__*/ createMotionComponentWithFeatures("select");
const MotionSmall = /*@__PURE__*/ createMotionComponentWithFeatures("small");
const MotionSource = /*@__PURE__*/ createMotionComponentWithFeatures("source");
const MotionSpan = /*@__PURE__*/ createMotionComponentWithFeatures("span");
const MotionStrong = /*@__PURE__*/ createMotionComponentWithFeatures("strong");
const MotionStyle = /*@__PURE__*/ createMotionComponentWithFeatures("style");
const MotionSub = /*@__PURE__*/ createMotionComponentWithFeatures("sub");
const MotionSummary = /*@__PURE__*/ createMotionComponentWithFeatures("summary");
const MotionSup = /*@__PURE__*/ createMotionComponentWithFeatures("sup");
const MotionTable = /*@__PURE__*/ createMotionComponentWithFeatures("table");
const MotionTbody = /*@__PURE__*/ createMotionComponentWithFeatures("tbody");
const MotionTd = /*@__PURE__*/ createMotionComponentWithFeatures("td");
const MotionTextarea = /*@__PURE__*/ createMotionComponentWithFeatures("textarea");
const MotionTfoot = /*@__PURE__*/ createMotionComponentWithFeatures("tfoot");
const MotionTh = /*@__PURE__*/ createMotionComponentWithFeatures("th");
const MotionThead = /*@__PURE__*/ createMotionComponentWithFeatures("thead");
const MotionTime = /*@__PURE__*/ createMotionComponentWithFeatures("time");
const MotionTitle = /*@__PURE__*/ createMotionComponentWithFeatures("title");
const MotionTr = /*@__PURE__*/ createMotionComponentWithFeatures("tr");
const MotionTrack = /*@__PURE__*/ createMotionComponentWithFeatures("track");
const MotionU = /*@__PURE__*/ createMotionComponentWithFeatures("u");
const MotionUl = /*@__PURE__*/ createMotionComponentWithFeatures("ul");
const MotionVideo = /*@__PURE__*/ createMotionComponentWithFeatures("video");
const MotionWbr = /*@__PURE__*/ createMotionComponentWithFeatures("wbr");
const MotionWebview = /*@__PURE__*/ createMotionComponentWithFeatures("webview");
/**
 * SVG components
 */
const MotionAnimate = /*@__PURE__*/ createMotionComponentWithFeatures("animate");
const MotionCircle = /*@__PURE__*/ createMotionComponentWithFeatures("circle");
const MotionDefs = /*@__PURE__*/ createMotionComponentWithFeatures("defs");
const MotionDesc = /*@__PURE__*/ createMotionComponentWithFeatures("desc");
const MotionEllipse = /*@__PURE__*/ createMotionComponentWithFeatures("ellipse");
const MotionG = /*@__PURE__*/ createMotionComponentWithFeatures("g");
const MotionImage = /*@__PURE__*/ createMotionComponentWithFeatures("image");
const MotionLine = /*@__PURE__*/ createMotionComponentWithFeatures("line");
const MotionFilter = /*@__PURE__*/ createMotionComponentWithFeatures("filter");
const MotionMarker = /*@__PURE__*/ createMotionComponentWithFeatures("marker");
const MotionMask = /*@__PURE__*/ createMotionComponentWithFeatures("mask");
const MotionMetadata = /*@__PURE__*/ createMotionComponentWithFeatures("metadata");
const MotionPath = /*@__PURE__*/ createMotionComponentWithFeatures("path");
const MotionPattern = /*@__PURE__*/ createMotionComponentWithFeatures("pattern");
const MotionPolygon = /*@__PURE__*/ createMotionComponentWithFeatures("polygon");
const MotionPolyline = /*@__PURE__*/ createMotionComponentWithFeatures("polyline");
const MotionRect = /*@__PURE__*/ createMotionComponentWithFeatures("rect");
const MotionStop = /*@__PURE__*/ createMotionComponentWithFeatures("stop");
const MotionSvg = /*@__PURE__*/ createMotionComponentWithFeatures("svg");
const MotionSymbol = /*@__PURE__*/ createMotionComponentWithFeatures("symbol");
const MotionText = /*@__PURE__*/ createMotionComponentWithFeatures("text");
const MotionTspan = /*@__PURE__*/ createMotionComponentWithFeatures("tspan");
const MotionUse = /*@__PURE__*/ createMotionComponentWithFeatures("use");
const MotionView = /*@__PURE__*/ createMotionComponentWithFeatures("view");
const MotionClipPath = /*@__PURE__*/ createMotionComponentWithFeatures("clipPath");
const MotionFeBlend = /*@__PURE__*/ createMotionComponentWithFeatures("feBlend");
const MotionFeColorMatrix = 
/*@__PURE__*/ createMotionComponentWithFeatures("feColorMatrix");
const MotionFeComponentTransfer = /*@__PURE__*/ createMotionComponentWithFeatures("feComponentTransfer");
const MotionFeComposite = 
/*@__PURE__*/ createMotionComponentWithFeatures("feComposite");
const MotionFeConvolveMatrix = 
/*@__PURE__*/ createMotionComponentWithFeatures("feConvolveMatrix");
const MotionFeDiffuseLighting = 
/*@__PURE__*/ createMotionComponentWithFeatures("feDiffuseLighting");
const MotionFeDisplacementMap = 
/*@__PURE__*/ createMotionComponentWithFeatures("feDisplacementMap");
const MotionFeDistantLight = 
/*@__PURE__*/ createMotionComponentWithFeatures("feDistantLight");
const MotionFeDropShadow = 
/*@__PURE__*/ createMotionComponentWithFeatures("feDropShadow");
const MotionFeFlood = /*@__PURE__*/ createMotionComponentWithFeatures("feFlood");
const MotionFeFuncA = /*@__PURE__*/ createMotionComponentWithFeatures("feFuncA");
const MotionFeFuncB = /*@__PURE__*/ createMotionComponentWithFeatures("feFuncB");
const MotionFeFuncG = /*@__PURE__*/ createMotionComponentWithFeatures("feFuncG");
const MotionFeFuncR = /*@__PURE__*/ createMotionComponentWithFeatures("feFuncR");
const MotionFeGaussianBlur = 
/*@__PURE__*/ createMotionComponentWithFeatures("feGaussianBlur");
const MotionFeImage = /*@__PURE__*/ createMotionComponentWithFeatures("feImage");
const MotionFeMerge = /*@__PURE__*/ createMotionComponentWithFeatures("feMerge");
const MotionFeMergeNode = 
/*@__PURE__*/ createMotionComponentWithFeatures("feMergeNode");
const MotionFeMorphology = 
/*@__PURE__*/ createMotionComponentWithFeatures("feMorphology");
const MotionFeOffset = /*@__PURE__*/ createMotionComponentWithFeatures("feOffset");
const MotionFePointLight = 
/*@__PURE__*/ createMotionComponentWithFeatures("fePointLight");
const MotionFeSpecularLighting = 
/*@__PURE__*/ createMotionComponentWithFeatures("feSpecularLighting");
const MotionFeSpotLight = 
/*@__PURE__*/ createMotionComponentWithFeatures("feSpotLight");
const MotionFeTile = /*@__PURE__*/ createMotionComponentWithFeatures("feTile");
const MotionFeTurbulence = 
/*@__PURE__*/ createMotionComponentWithFeatures("feTurbulence");
const MotionForeignObject = 
/*@__PURE__*/ createMotionComponentWithFeatures("foreignObject");
const MotionLinearGradient = 
/*@__PURE__*/ createMotionComponentWithFeatures("linearGradient");
const MotionRadialGradient = 
/*@__PURE__*/ createMotionComponentWithFeatures("radialGradient");
const MotionTextPath = /*@__PURE__*/ createMotionComponentWithFeatures("textPath");

export { MotionA, MotionAbbr, MotionAddress, MotionAnimate, MotionArea, MotionArticle, MotionAside, MotionAudio, MotionB, MotionBase, MotionBdi, MotionBdo, MotionBig, MotionBlockquote, MotionBody, MotionButton, MotionCanvas, MotionCaption, MotionCircle, MotionCite, MotionClipPath, MotionCode, MotionCol, MotionColgroup, MotionData, MotionDatalist, MotionDd, MotionDefs, MotionDel, MotionDesc, MotionDetails, MotionDfn, MotionDialog, MotionDiv, MotionDl, MotionDt, MotionEllipse, MotionEm, MotionEmbed, MotionFeBlend, MotionFeColorMatrix, MotionFeComponentTransfer, MotionFeComposite, MotionFeConvolveMatrix, MotionFeDiffuseLighting, MotionFeDisplacementMap, MotionFeDistantLight, MotionFeDropShadow, MotionFeFlood, MotionFeFuncA, MotionFeFuncB, MotionFeFuncG, MotionFeFuncR, MotionFeGaussianBlur, MotionFeImage, MotionFeMerge, MotionFeMergeNode, MotionFeMorphology, MotionFeOffset, MotionFePointLight, MotionFeSpecularLighting, MotionFeSpotLight, MotionFeTile, MotionFeTurbulence, MotionFieldset, MotionFigcaption, MotionFigure, MotionFilter, MotionFooter, MotionForeignObject, MotionForm, MotionG, MotionH1, MotionH2, MotionH3, MotionH4, MotionH5, MotionH6, MotionHead, MotionHeader, MotionHgroup, MotionHr, MotionHtml, MotionI, MotionIframe, MotionImage, MotionImg, MotionInput, MotionIns, MotionKbd, MotionKeygen, MotionLabel, MotionLegend, MotionLi, MotionLine, MotionLinearGradient, MotionLink, MotionMain, MotionMap, MotionMark, MotionMarker, MotionMask, MotionMenu, MotionMenuitem, MotionMetadata, MotionMeter, MotionNav, MotionObject, MotionOl, MotionOptgroup, MotionOption, MotionOutput, MotionP, MotionParam, MotionPath, MotionPattern, MotionPicture, MotionPolygon, MotionPolyline, MotionPre, MotionProgress, MotionQ, MotionRadialGradient, MotionRect, MotionRp, MotionRt, MotionRuby, MotionS, MotionSamp, MotionScript, MotionSection, MotionSelect, MotionSmall, MotionSource, MotionSpan, MotionStop, MotionStrong, MotionStyle, MotionSub, MotionSummary, MotionSup, MotionSvg, MotionSymbol, MotionTable, MotionTbody, MotionTd, MotionText, MotionTextPath, MotionTextarea, MotionTfoot, MotionTh, MotionThead, MotionTime, MotionTitle, MotionTr, MotionTrack, MotionTspan, MotionU, MotionUl, MotionUse, MotionVideo, MotionView, MotionWbr, MotionWebview };
