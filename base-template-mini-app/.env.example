# Redis 
KV_REST_API_TOKEN=''
KV_REST_API_URL=''

# Neynar
NEYNAR_API_KEY=  
NEYNAR_CLIENT_ID= #If using neynar for notification

# Manifest 
ACCOUNT_ASSOCIATION_HEADER=
ACCOUNT_ASSOCIATION_PAYLOAD=
ACCOUNT_ASSOCIATION_SIGNATURE=

# Mini App 
NEXT_PUBLIC_URL='https://localhost:3000'
NEXT_PUBLIC_FRAME_NAME=
NEXT_PUBLIC_FRAME_DESCRIPTION=
NEXT_PUBLIC_FRAME_PRIMARY_CATEGORY=
NEXT_PUBLIC_FRAME_TAGS=
NEXT_PUBLIC_FRAME_BUTTON_TEXT=
NEXT_PUBLIC_ANALYTICS_ENABLED=
NEXT_PUBLIC_USE_WALLET="true"
USE_TUNNEL="true"
