{"name": "farcaster-mini-app-template", "version": "0.1.0", "type": "module", "private": false, "access": "public", "exports": {".": {"types": "./index.d.ts", "import": "./bin/init.js"}}, "types": "./index.d.ts", "scripts": {"dev": "node scripts/dev.js", "build": "node scripts/build.js", "start": "next start", "lint": "next lint", "deploy:vercel": "node scripts/deploy.js", "cleanup": "lsof -ti :3000 | xargs kill -9"}, "dependencies": {"@farcaster/auth-client": ">=0.3.0 <1.0.0", "@farcaster/auth-kit": ">=0.6.0 <1.0.0", "@farcaster/frame-core": ">=0.0.29 <1.0.0", "@farcaster/frame-node": ">=0.0.18 <1.0.0", "@farcaster/frame-sdk": ">=0.0.31 <1.0.0", "@farcaster/frame-wagmi-connector": ">=0.0.19 <1.0.0", "@neynar/nodejs-sdk": "^2.46.0", "@neynar/react": "^1.2.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-slot": "^1.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "dotenv": "^16.5.0", "lucide-react": "^0.469.0", "mipd": "^0.0.7", "next": "^15", "react": "^19", "react-dom": "^19", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "viem": "^2.23.6", "wagmi": "^2.14.12", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "crypto": "^1.0.1", "eslint": "^8", "eslint-config-next": "15.0.3", "localtunnel": "^2.0.2", "pino-pretty": "^13.0.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}