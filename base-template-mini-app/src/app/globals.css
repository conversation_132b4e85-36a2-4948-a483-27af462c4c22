@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
  --card: #ffffff;
  --card-foreground: #171717;
  --popover: #ffffff;
  --popover-foreground: #171717;
  --primary: #2563eb;
  --primary-foreground: #ffffff;
  --secondary: #f3f4f6;
  --secondary-foreground: #1f2937;
  --muted: #f3f4f6;
  --muted-foreground: #6b7280;
  --accent: #f3f4f6;
  --accent-foreground: #1f2937;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e5e7eb;
  --input: #e5e7eb;
  --ring: #2563eb;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --card: #171717;
    --card-foreground: #ededed;
    --popover: #171717;
    --popover-foreground: #ededed;
    --primary: #3b82f6;
    --primary-foreground: #ffffff;
    --secondary: #1f2937;
    --secondary-foreground: #f3f4f6;
    --muted: #1f2937;
    --muted-foreground: #9ca3af;
    --accent: #1f2937;
    --accent-foreground: #f3f4f6;
    --destructive: #ef4444;
    --destructive-foreground: #ffffff;
    --border: #374151;
    --input: #374151;
    --ring: #3b82f6;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: "Inter", Helvetica, Arial, sans-serif;
}

* {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

*::-webkit-scrollbar {
  display: none;
}

@layer base {
  :root {
    --radius: 0.5rem;
  }
}
