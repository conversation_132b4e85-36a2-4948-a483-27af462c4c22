<!-- generated by @neynar/create-farcaster-mini-app version 1.3.2 -->

# Farcaster Eth Mini App Template

A template for building Farcaster mini apps with Next.js, QuickAuth, and Ethereum integration.

## Getting Started

1. Use this template to create a new repository
2. Clone the repository to your local system:

```bash
git clone <your-repo-url>
cd <your-repo-name>
```

3. Set up required services:

   - **Redis** (Required for notifications / Optional if using Neynar):

     - Get your URL and Key from [Upstash Console](https://console.upstash.com/redis)
     - Used for sending notifications to users

   - **Neynar API** (Optional):
     - Get your API Key from [Neynar Developer Portal](https://dev.neynar.com/app)
     - Used for fetching Farcaster data
     - Can be used for notifications if you don't want to use Redis

4. Install dependencies:

```bash
npm install
```

5. Run the development server:

```bash
npm run dev
```

## Deployment

1. Deploy to Vercel:

2. Claim Mini App Ownership:

   - Go to [Farcaster Mini Apps Manifest](https://farcaster.xyz/~/developers/mini-apps/manifest)
   - Click "Claim Ownership" (required for sending notifications and developer rewards)
   - Sign using your phone
   - Update environment variables with:
     ```
     ACCOUNT_ASSOCIATION_HEADER
     ACCOUNT_ASSOCIATION_PAYLOAD
     ACCOUNT_ASSOCIATION_SIGNATURE
     ```

3. Redeploy to update the build with new environment variables

## Authentication

### Making Authenticated Requests

1. Import the auth utilities:

```typescript
import { fetchWithAuth } from "~/lib/auth";
```

2. Use `fetchWithAuth` for API calls:

```typescript
// Example API call
const response = await fetchWithAuth("/api/protected-route");
const data = await response.json();
```

### Protected API Routes

1. Import the auth utilities:

```typescript
import { verifyAuth } from "~/lib/auth";
```

2. Verify authentication in your route:

```typescript
export async function GET(request: Request) {
  const auth = await verifyAuth(request);
  if (!auth) {
    return new NextResponse("Unauthorized", { status: 401 });
  }

  // Your protected route logic here
  return NextResponse.json({ fid: auth.fid });
}
```

## Learn More

- [MiniApps Documentation](https://miniapps.farcaster.xyz/)
- [Next.js Documentation](https://nextjs.org/docs)

## Guide

This is made using Neynar's StarterKit [this Neynar docs page](https://docs.neynar.com/docs/create-farcaster-miniapp-in-60s) for a simple guide on how to create a Farcaster Mini App in less than 60 seconds!

## Getting Started

To create a new mini app project, run:

Click on use this template.

To run the project:

```{bash}
cd <PROJECT_NAME>
npm run dev
```

## Building for Production

To create a production build, run:

```{bash}
npm run build
```

The above command will generate a `.env` file based on the `.env.local` file and user input. Be sure to configure those environment variables on your hosting platform.

## Building with AI

AI guide for FC Mini Apps SDK - https://miniapps.farcaster.xyz/docs/getting-started#building-with-ai

AI guide for Neynar - https://docs.neynar.com/docs/neynar-farcaster-with-cursor

## Why MiniApps?

Lets u open Apps inside social feed.
Leverage and build on social Data so add social component to make your app viral.
Send Notifications to retain the users.

Simple flow

- Build your simple App just like u bulid webapps
- Wrap it inside the NeynarProvider (FrameContext) to get the context of the user [username, fid, walletadd, pfp and with fid his entire social graph]
- Access the inbuilt Farcaster Wallet for any onchain activity
